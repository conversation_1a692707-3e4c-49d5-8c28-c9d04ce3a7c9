App({
  globalData: {
    userInfo: null,
    isPaid: false,
    interviewAnswers: [],
    historyRecords: []
  },

  onLaunch: function () {
    console.log('SmartHire小程序启动')

    // 初始化全局数据
    this.initGlobalData()
  },

  // 初始化全局数据
  initGlobalData: function() {
    // 可以从本地存储加载数据
    try {
      const historyData = tt.getStorageSync('historyRecords')
      if (historyData) {
        this.globalData.historyRecords = JSON.parse(historyData)
      }
    } catch (e) {
      console.log('加载历史记录失败:', e)
    }
  },

  // 保存历史记录到本地存储
  saveHistoryToStorage: function() {
    try {
      tt.setStorageSync('historyRecords', JSON.stringify(this.globalData.historyRecords))
    } catch (e) {
      console.log('保存历史记录失败:', e)
    }
  }
})
