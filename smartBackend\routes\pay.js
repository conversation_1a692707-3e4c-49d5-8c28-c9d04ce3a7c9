// backend/routes/pay.js
import express from 'express';
import axios from 'axios';
import { query } from '../utils/database.js';
import { generateDouyinPaySign } from '../utils/douyinPaySign.js';

const router = express.Router();

const DOUYIN_APP_ID_PAY = "tt04c2727f9bcae58501"; // 支付侧的APPID，可能与小程序APPID不同
const DOUYIN_NOTIFY_URL = "https://01e6-240e-343-5849-4f00-4981-edb6-3ce0-db1a.ngrok-free.app/api/pay/notify"; // 支付回调地址

router.post('/createOrder', async (req, res) => {
    const { userId, openId, amount, productDescription, companyName, recruitmentRequirements, userName } = req.body;

    // 1. 生成内部订单号
    const order_sn = `SHIRE${Date.now()}${Math.floor(Math.random() * 1000)}`;
    const total_amount_fen = Math.round(amount * 100); // 抖音支付金额单位为分

    try {
        // 2. 将订单信息存入数据库，状态为PENDING
        const orderInsertRes = await query(
            'INSERT INTO orders (user_id, order_sn, amount, product_description, company_name, recruitment_requirements, user_name, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id',
            [userId, order_sn, amount, productDescription, companyName, recruitmentRequirements, userName, 'PENDING']
        );

        console.log('数据库插入结果:', orderInsertRes);

        if (!orderInsertRes || orderInsertRes.length === 0) {
            throw new Error('订单插入失败，未返回订单ID');
        }

        const orderId = orderInsertRes[0].id;

        // 3. 准备抖音支付参数 - 严格按照文档要求
        const paramsForDouyin = {
            app_id: DOUYIN_APP_ID_PAY,
            out_order_no: order_sn, // 您的内部订单号
            total_amount: total_amount_fen,
            subject: productDescription,
            body: `为 ${companyName} 定制模拟面试`, // 商品详情
            valid_time: 900, // 15分钟，文档要求最小300秒
            notify_url: DOUYIN_NOTIFY_URL,
            // 移除不支持的参数：merchant_id, trade_type, version, timestamp, nonce_str, customer_id
        };

        // 4. 对参数进行签名
        const sign = generateDouyinPaySign(paramsForDouyin); // 假设签名函数已实现
        paramsForDouyin.sign = sign;

        // 5. 调用抖音支付统一支付接口
        const douyinPayRes = await axios.post('https://developer.toutiao.com/api/apps/ecpay/v1/create_order', paramsForDouyin, {
            headers: { 'Content-Type': 'application/json' }
        });

        console.log('抖音支付API响应:', douyinPayRes.data);

        if (douyinPayRes.data.err_no !== 0) {
            console.error('抖音支付创建订单失败:', douyinPayRes.data);
            // 订单状态标记为FAILED
            await query('UPDATE orders SET status = $1 WHERE id = $2', ['FAILED', orderId]);
            return res.status(500).json({
                code: -1,
                msg: douyinPayRes.data.err_tips || '抖音支付创建订单失败',
                error_details: douyinPayRes.data
            });
        }

        const { order_id, order_token } = douyinPayRes.data.data;
        // 更新订单表中的抖音订单ID
        await query('UPDATE orders SET douyin_order_id = $1, order_token = $2 WHERE id = $3', [order_id, order_token, orderId]);

        // 返回order_token给前端用于支付
        res.json({
            code: 0,
            msg: '订单创建成功',
            data: {
                order_id: order_id,
                order_token: order_token,
                order_sn: order_sn
            }
        });







    } catch (error) {
        console.error('创建订单失败:', error);
        res.status(500).json({ code: -1, msg: '服务器错误' });
    }
});

export default router;