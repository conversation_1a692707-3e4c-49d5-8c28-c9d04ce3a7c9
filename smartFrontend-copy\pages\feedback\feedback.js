const app = getApp()

Page({
  data: {
    score: 85,
    strengths: [
      '沟通能力出色，回答清晰简洁。',
      '对职位要求有深刻理解。',
      '展现出积极主动的学习态度。'
    ],
    improvements: [
      '可以提供更具体的例子来支持论述。',
      '考虑详细阐述过往经历以更有效地展示能力。'
    ],
    rating: 0,
    comment: ''
  },
  
  onLoad: function () {
    this.generateFeedback()
    console.log('面试反馈页面加载完成')
  },
  
  // 生成面试反馈
  generateFeedback: function() {
    // 这里可以根据用户的回答生成个性化反馈
    // 现在使用模拟数据
    const scores = [75, 80, 85, 90, 95]
    const randomScore = scores[Math.floor(Math.random() * scores.length)]
    
    this.setData({
      score: randomScore
    })
  },
  
  // 返回按钮
  goBack: function() {
    tt.navigateBack()
  },
  
  // 保存反馈
  saveFeedback: function() {
    const { score, strengths, improvements } = this.data
    const userInfo = app.globalData.userInfo
    
    // 保存面试记录
    const interviewRecord = {
      companyName: userInfo ? userInfo.companyName : '未知公司',
      jobTitle: userInfo ? userInfo.jobTitle : '未知职位',
      score: score,
      date: new Date().toISOString().split('T')[0],
      strengths: strengths,
      improvements: improvements
    }
    
    // 获取现有记录
    let historyRecords = app.globalData.historyRecords || []
    historyRecords.unshift(interviewRecord) // 添加到开头
    
    // 限制记录数量
    if (historyRecords.length > 20) {
      historyRecords = historyRecords.slice(0, 20)
    }
    
    app.globalData.historyRecords = historyRecords
    
    tt.showToast({
      title: '反馈已保存',
      icon: 'success',
      duration: 2000
    })
  },
  
  // 设置星级评分
  setRating: function(e) {
    const rating = e.currentTarget.dataset.rating
    this.setData({
      rating: rating
    })
  },
  
  // 评价输入
  onCommentInput: function(e) {
    this.setData({
      comment: e.detail.value
    })
  },
  
  // 提交评价
  submitEvaluation: function() {
    const { rating, comment } = this.data
    
    if (rating === 0) {
      tt.showToast({
        title: '请选择星级评分',
        icon: 'none'
      })
      return
    }
    
    // 保存评价数据
    const evaluation = {
      rating: rating,
      comment: comment,
      date: new Date().toISOString()
    }
    
    console.log('用户评价:', evaluation)
    
    tt.showToast({
      title: '评价提交成功',
      icon: 'success',
      duration: 2000
    })
    
    // 延迟跳转到首页
    setTimeout(() => {
      tt.switchTab({
        url: '/pages/index/index'
      })
    }, 2000)
  }
})
