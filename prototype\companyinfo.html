<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-white">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden" style='font-family: Manrope, "Noto Sans", sans-serif;'>
<div class="flex-grow">
<header class="sticky top-0 z-10 bg-white shadow-sm">
<div class="mx-auto flex max-w-md items-center p-4">
<div class="flex-1">
<button class="flex items-center justify-center rounded-full text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#1383eb]">
<span class="material-icons text-2xl">arrow_back_ios_new</span>
</button>
</div>
<h1 class="flex-grow text-center text-lg font-bold text-gray-900">关于华为</h1>
<div class="flex-1 text-right">
<button class="flex items-center justify-center rounded-full text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#1383eb]">
<span class="material-icons text-2xl">help_outline</span>
</button>
</div>
</div>
</header>
<main class="mx-auto max-w-md px-4 py-6">
<div class="mb-6 rounded-lg bg-blue-50 p-4">
<p class="text-sm font-medium text-blue-700">💡 准备充足，面试更有底气！</p>
</div>
<section class="mb-8">
<h2 class="mb-3 text-xl font-bold text-gray-900">公司信息</h2>
<div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
<p class="text-base text-gray-700">此处将展示详细的公司文化、主营业务、近期新闻等</p>
</div>
</section>
</main>
</div>
<footer class="sticky bottom-0 bg-white py-4 shadow-[0_-2px_10px_-3px_rgba(0,0,0,0.1)]">
<div class="mx-auto max-w-md px-4">
<button class="w-full rounded-full bg-[#1383eb] py-3.5 text-base font-bold text-white shadow-md transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            开始模拟面试
          </button>
</div>
</footer>
</div>

</body></html>