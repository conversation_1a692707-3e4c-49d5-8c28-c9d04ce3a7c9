const app = getApp()

Page({
  data: {
    currentQuestion: 1,
    totalQuestions: 5,
    questionText: '',
    userAnswer: '',
    isLastQuestion: false,
    questions: [
      '请您介绍一下您对前端框架Vue.js的理解？',
      '描述一下您在团队协作中遇到的挑战以及如何解决的？',
      '您如何看待技术发展趋势，以及如何保持自己的技术竞争力？',
      '请分享一个您最有成就感的项目经历？',
      '您对我们公司的了解有多少，为什么想加入我们？'
    ],
    answers: []
  },
  
  onLoad: function () {
    this.loadCurrentQuestion()
    console.log('模拟面试页面加载完成')
  },
  
  // 加载当前问题
  loadCurrentQuestion: function() {
    const { currentQuestion, totalQuestions, questions } = this.data
    const questionText = questions[currentQuestion - 1]
    const isLastQuestion = currentQuestion === totalQuestions
    
    this.setData({
      questionText,
      isLastQuestion,
      userAnswer: '' // 清空答案输入框
    })
  },
  
  // 答案输入事件
  onAnswerInput: function(e) {
    this.setData({
      userAnswer: e.detail.value
    })
  },
  
  // 帮助按钮
  showHelp: function() {
    tt.showModal({
      title: '面试提示',
      content: '请认真回答每个问题，建议从具体例子出发，展示您的能力和经验。回答要条理清晰，重点突出。',
      showCancel: false,
      confirmText: '知道了'
    })
  },
  
  // 提交答案
   submitAnswer: function() {
    console.log('submitAnswer')
    const { userAnswer, currentQuestion, totalQuestions, answers, questions } = this.data
    
    if (!userAnswer.trim()) {
      tt.showToast({
        title: '请输入您的回答',
        icon: 'none'
      })
      return
    }
    
    // 保存当前答案
    const newAnswers = [...answers]
    newAnswers[currentQuestion - 1] = {
      question: questions[currentQuestion - 1],
      answer: userAnswer.trim()
    }
    
    if (currentQuestion < totalQuestions) {
      // 不是最后一题，继续下一题
      this.setData({
        currentQuestion: currentQuestion + 1,
        answers: newAnswers
      })
      this.loadCurrentQuestion()
      
      tt.showToast({
        title: '答案已保存',
        icon: 'success',
        duration: 1000
      })
    } else {
      // 最后一题，完成面试
      this.setData({
        answers: newAnswers
      })
      
      // 保存面试答案到全局
      app.globalData.interviewAnswers = newAnswers
      
      tt.showToast({
        title: '面试完成！',
        icon: 'success',
        duration: 2000
      })
      
      // 跳转到反馈页面
      setTimeout(() => {
        tt.redirectTo({
          url: '/pages/feedback/feedback'
        })
      }, 2000)
    }
  }
})
