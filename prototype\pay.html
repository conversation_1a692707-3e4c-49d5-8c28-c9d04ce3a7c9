<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支付页面</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    .status-bar {
      background-color: #f8f8f8;
      height: 44px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      font-size: 12px;
      color: #000;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .tab-bar {
      background-color: #f8f8f8;
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      border-top: 1px solid #e5e5e5;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
    }
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 10px;
    }
    .payment-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: calc(100vh - 94px);
      padding: 20px;
      text-align: center;
    }
    .pay-button {
      background-color: #f59e0b;
      color: white;
      font-weight: bold;
      padding: 16px 40px;
      border-radius: 30px;
      font-size: 18px;
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }
    .pay-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #f59e0b;
      color: white;
      margin-bottom: 16px;
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }
    .payment-methods {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-top: 30px;
    }
    .payment-method {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #e5e5e5;
    }
    .payment-method-icon {
      width: 24px;
      margin-right: 12px;
      color: #4b5563;
    }
    .selected {
      color: #f59e0b;
    }
  </style>
</head>
<body class="bg-gray-100">


  <!-- 支付内容 -->
  <div class="payment-container">
    <div class="pay-icon">
      <i class="fas fa-unlock-alt text-3xl"></i>
    </div>
    
    <h1 class="text-2xl font-bold mb-2">解锁公司信息及模拟面试问题</h1>
    <p class="text-gray-500 mb-6">支付¥19.9元，立即查看公司信息及模拟面试问题</p>
    
    <div class="bg-white rounded-lg w-full p-4 mb-6">
      <div class="flex justify-between items-center">
        <div>
          <div class="text-lg font-semibold">详情解锁服务</div>
          <div class="text-gray-500 text-sm">含模拟面试问题</div>
        </div>
        <div class="text-xl font-bold text-f59e0b">¥19.9</div>
      </div>
    </div>
    
    <div class="payment-methods">
      <div class="bg-white rounded-lg overflow-hidden">
        <div class="payment-method">
          <i class="fab fa-weixin payment-method-icon text-green-500"></i>
          <span>微信支付</span>
          <i class="fas fa-check-circle ml-auto selected"></i>
        </div>
        <div class="payment-method">
          <i class="fab fa-alipay payment-method-icon text-green-500"></i>
          <span>支付宝支付</span>
          <i class="fas fa-check-circle ml-auto selected"></i>
        </div>
        
      
      </div>
    </div>
    
    <button class="pay-button mt-10 w-full">立即支付 ¥19.9</button>
    
    <p class="text-gray-400 text-xs mt-4">
      支付即视为同意《SmartHire解锁服务协议》
    </p>
  </div>


</body>
</html> 