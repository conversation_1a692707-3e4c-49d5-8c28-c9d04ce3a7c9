# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smart_hire
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# 抖音小程序配置
DOUYIN_APP_ID=tt04c2727f9bcae58501
DOUYIN_APP_SECRET=your_app_secret

# 抖音支付配置
DOUYIN_APP_ID_PAY=tt04c2727f9bcae58501
DOUYIN_MERCHANT_ID=your_merchant_id

# 抖音支付RSA私钥 (请替换为您在抖音开放平台生成的真实私钥)
# 注意：私钥应该包含完整的BEGIN和END标记，并保持换行格式
DOUYIN_RSA_PRIVATE_KEY="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 抖音支付API密钥 (用于MD5签名，如果需要)
DOUYIN_API_KEY=your_api_key

# 服务器配置
PORT=3000
NODE_ENV=development

# 支付回调地址
DOUYIN_NOTIFY_URL=https://your-domain.com/api/pay/notify
