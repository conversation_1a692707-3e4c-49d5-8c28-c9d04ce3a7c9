.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F7F8FA;
}

.header {
  background-color: #ffffff;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #111518;
  text-align: center;
}

.tip-section {
  background-color: #E6F2FD;
  padding: 24rpx;
  margin: 32rpx;
  border-radius: 16rpx;
}

.tip-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #0B80EE;
  text-align: center;
}

.content {
  flex: 1;
  padding: 16rpx 32rpx;
}

.locked-info {
  background: linear-gradient(135deg, rgba(0,0,0,0.5), rgba(0,0,0,0.3));
  border-radius: 24rpx;
  min-height: 600rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.locked-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.2;
}

.lock-overlay {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 48rpx;
}

.lock-icon {
  font-size: 100rpx;
  margin-bottom: 32rpx;
  opacity: 0.7;
}

.lock-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.lock-description {
  font-size: 28rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  max-width: 480rpx;
}

.footer {
  position: sticky;
  bottom: 0;
  background-color: #ffffff;
  padding: 32rpx;
  border-top: 1rpx solid #e5e7eb;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.unlock-button {
  width: 100%;
  height: 112rpx;
  background-color: #0B80EE;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 56rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(11, 128, 238, 0.3);
  transition: all 0.2s ease;
  line-height: 112rpx;
}

.unlock-button:active {
  background-color: #0968C3;
  transform: translateY(2rpx);
}
