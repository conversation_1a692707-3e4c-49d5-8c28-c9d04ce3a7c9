// 数据库初始化脚本
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { query } from './utils/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function initDatabase() {
    try {
        console.log('开始初始化数据库...');
        
        // 读取SQL文件
        const sqlFile = path.join(__dirname, 'init-database.sql');
        const sql = fs.readFileSync(sqlFile, 'utf8');
        
        // 分割SQL语句（简单的分割，基于分号）
        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
        
        // 执行每个SQL语句
        for (const statement of statements) {
            const trimmedStatement = statement.trim();
            if (trimmedStatement) {
                console.log('执行SQL:', trimmedStatement.substring(0, 50) + '...');
                await query(trimmedStatement, []);
            }
        }
        
        console.log('✅ 数据库初始化完成！');
        
        // 验证表是否创建成功
        const tables = await query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `, []);
        
        console.log('📋 已创建的表:');
        tables.forEach(table => {
            console.log(`  - ${table.table_name}`);
        });
        
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error);
        process.exit(1);
    }
}

// 运行初始化
initDatabase();
