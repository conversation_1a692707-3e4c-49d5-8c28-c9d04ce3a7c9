<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    .material-icons-outlined {
      font-size: inherit; 
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-50">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden" style='font-family: Manrope, "Noto Sans", sans-serif;'>
<div class="flex-grow">
<header class="sticky top-0 z-10 bg-white shadow-sm">
<div class="flex items-center p-4 justify-center">
<h1 class="text-gray-800 text-xl font-semibold leading-tight">我的面试记录</h1>
</div>
</header>
<main class="p-4 space-y-3">
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">华为</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2024-01-15</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">85</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">字节跳动</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2024-01-10</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">92</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">腾讯</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2024-01-05</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">78</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">阿里巴巴</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-12-20</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">88</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">美团</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-12-15</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">75</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">小米</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-12-10</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">90</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">百度</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-12-05</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">82</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">网易</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-11-20</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">86</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">京东</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-11-15</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">79</p>
</div>
</div>
<div class="flex items-center gap-4 bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
<div class="text-white flex items-center justify-center rounded-full bg-blue-500 shrink-0 size-10">
<span class="material-icons-outlined text-xl">
              description
            </span>
</div>
<div class="flex-grow flex flex-col justify-center">
<p class="text-gray-800 text-base font-medium leading-normal">拼多多</p>
<p class="text-gray-500 text-sm font-normal leading-normal">2023-11-10</p>
</div>
<div class="shrink-0">
<p class="text-blue-600 text-lg font-semibold leading-normal">84</p>
</div>
</div>
</main>
</div>
</div>

</body></html>