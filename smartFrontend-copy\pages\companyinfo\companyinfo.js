const app = getApp()

Page({
  data: {
    companyName: '',
    companyInfo: '华为技术有限公司成立于1987年，是全球领先的ICT（信息与通信）基础设施和智能终端提供商。公司致力于把数字世界带入每个人、每个家庭、每个组织，构建万物互联的智能世界。\n\n华为在通信网络、IT、智能终端和云服务等领域为客户提供有竞争力、安全可信赖的产品、解决方案与服务。目前华为约有19.4万员工，业务遍及170多个国家和地区，服务30多亿人口。\n\n公司文化：以客户为中心，以奋斗者为本，长期艰苦奋斗。华为坚持每年将10%以上的销售收入投入研究与开发，近十年累计投入的研发费用超过4800亿人民币。'
  },
  
  onLoad: function () {
    // 获取全局用户信息
    const userInfo = app.globalData.userInfo
    if (userInfo && userInfo.companyName) {
      this.setData({
        companyName: userInfo.companyName
      })
      
      // 根据不同公司显示不同信息
      this.loadCompanyInfo(userInfo.companyName)
    }
    console.log('公司信息页面加载完成')
  },
  
  // 加载公司信息
  loadCompanyInfo: function(companyName) {
    // 这里可以根据公司名称加载不同的信息
    // 现在使用默认的华为信息作为示例
    let info = ''
    
    switch(companyName.toLowerCase()) {
      case '华为':
      case 'huawei':
        info = '华为技术有限公司成立于1987年，是全球领先的ICT（信息与通信）基础设施和智能终端提供商。公司致力于把数字世界带入每个人、每个家庭、每个组织，构建万物互联的智能世界。\n\n华为在通信网络、IT、智能终端和云服务等领域为客户提供有竞争力、安全可信赖的产品、解决方案与服务。目前华为约有19.4万员工，业务遍及170多个国家和地区，服务30多亿人口。\n\n公司文化：以客户为中心，以奋斗者为本，长期艰苦奋斗。华为坚持每年将10%以上的销售收入投入研究与开发，近十年累计投入的研发费用超过4800亿人民币。'
        break
      case '腾讯':
      case 'tencent':
        info = '腾讯成立于1998年11月，是一家以互联网为基础的科技与文化公司。腾讯的使命是"用户为本，科技向善"。\n\n腾讯的产品及服务包括社交和通信服务QQ及微信/WeChat、社交网络平台QQ空间、腾讯游戏旗下QQ游戏平台、门户网站腾讯网、腾讯新闻客户端和网络视频服务腾讯视频等。\n\n公司文化：正直、进取、协作、创新。腾讯一直秉承"一切以用户价值为依归"的经营理念，为亿万网民提供优质的互联网综合服务。'
        break
      default:
        info = `${companyName}是一家优秀的企业，致力于为客户提供高质量的产品和服务。公司拥有专业的团队和先进的技术，在行业内享有良好的声誉。\n\n公司注重员工发展，提供良好的工作环境和成长机会。企业文化强调团队合作、创新精神和客户至上的理念。\n\n我们期待与优秀的人才共同发展，为公司的未来贡献力量。`
    }
    
    this.setData({
      companyInfo: info
    })
  },
  
  // 返回按钮
  goBack: function() {
    tt.navigateBack()
  },
  
  // 帮助按钮
  showHelp: function() {
    tt.showModal({
      title: '帮助',
      content: '这里显示的是根据您输入的公司名称生成的详细公司信息，包括公司背景、业务范围、企业文化等内容，帮助您更好地准备面试。',
      showCancel: false,
      confirmText: '知道了'
    })
  },
  
  // 开始模拟面试
  startInterview: function() {
    // 跳转到模拟面试页面
    tt.navigateTo({
      url: '/pages/question/question'
    })
  }
})
