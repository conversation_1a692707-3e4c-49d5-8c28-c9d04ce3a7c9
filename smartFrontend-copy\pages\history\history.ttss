.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #374151;
}

.content {
  flex: 1;
  padding: 32rpx;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 32rpx;
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.history-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #3b82f6;
  flex-shrink: 0;
}

.icon-text {
  font-size: 40rpx;
  color: #ffffff;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.company-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8rpx;
}

.interview-date {
  font-size: 24rpx;
  color: #6b7280;
}

.item-score {
  flex-shrink: 0;
}

.score-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #2563eb;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 120rpx 40rpx;
  min-height: 600rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 48rpx;
  max-width: 480rpx;
}

.start-interview-button {
  width: 320rpx;
  height: 88rpx;
  background-color: #0c7ff2;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(12, 127, 242, 0.3);
  transition: all 0.2s ease;
}

.start-interview-button:active {
  background-color: #0968c3;
  transform: translateY(2rpx);
}
