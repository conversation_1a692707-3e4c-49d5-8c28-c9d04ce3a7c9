-- SmartHire 数据库初始化脚本

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    douyin_openid VARCHAR(255) UNIQUE NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    order_sn VARCHAR(100) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    product_description VARCHAR(500),
    company_name VARCHAR(200),
    recruitment_requirements TEXT,
    user_name VARCHAR(100),
    status VARCHAR(50) DEFAULT 'PENDING',
    douyin_order_id VARCHAR(200),
    order_token TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建公司信息表
CREATE TABLE IF NOT EXISTS company_info (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(200) NOT NULL,
    description TEXT,
    industry VARCHAR(100),
    size VARCHAR(50),
    location VARCHAR(200),
    website VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建面试记录表
CREATE TABLE IF NOT EXISTS interview_records (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    order_id INTEGER REFERENCES orders(id),
    company_name VARCHAR(200),
    job_title VARCHAR(200),
    score INTEGER,
    strengths TEXT,
    improvements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建面试问题表
CREATE TABLE IF NOT EXISTS interview_questions (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(200),
    job_title VARCHAR(200),
    question TEXT NOT NULL,
    category VARCHAR(100),
    difficulty VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建面试答案表
CREATE TABLE IF NOT EXISTS interview_answers (
    id SERIAL PRIMARY KEY,
    interview_record_id INTEGER REFERENCES interview_records(id),
    question_id INTEGER REFERENCES interview_questions(id),
    question_text TEXT,
    user_answer TEXT,
    score INTEGER,
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入一些示例数据
INSERT INTO company_info (company_name, description, industry, size, location) VALUES
('华为', '华为技术有限公司成立于1987年，是全球领先的ICT（信息与通信）基础设施和智能终端提供商。', '通信技术', '大型企业', '深圳')
ON CONFLICT DO NOTHING;

INSERT INTO interview_questions (company_name, job_title, question, category, difficulty) VALUES
('华为', '前端工程师', '请您介绍一下您对前端框架Vue.js的理解？', '技术能力', '中等'),
('华为', '前端工程师', '描述一下您在团队协作中遇到的挑战以及如何解决的？', '团队协作', '中等'),
('华为', '前端工程师', '您如何看待技术发展趋势，以及如何保持自己的技术竞争力？', '职业发展', '中等'),
('华为', '前端工程师', '请分享一个您最有成就感的项目经历？', '项目经验', '中等'),
('华为', '前端工程师', '您对我们公司的了解有多少，为什么想加入我们？', '公司了解', '基础')
ON CONFLICT DO NOTHING;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_openid ON users(douyin_openid);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_interview_records_user_id ON interview_records(user_id);
CREATE INDEX IF NOT EXISTS idx_company_info_name ON company_info(company_name);
