const app = getApp()

Page({
  data: {
    historyList: []
  },
  
  onLoad: function () {
    this.loadHistoryData()
    console.log('历史记录页面加载完成')
  },
  
  onShow: function() {
    // 每次显示页面时重新加载数据
    this.loadHistoryData()
  },
  
  // 加载历史数据
  loadHistoryData: function() {
    const historyRecords = app.globalData.historyRecords || []
    
    // 如果没有历史记录，创建一些示例数据
    if (historyRecords.length === 0) {
      const sampleData = [
        {
          companyName: '华为',
          jobTitle: '前端工程师',
          score: 85,
          date: '2024-01-15',
          strengths: ['沟通能力出色', '技术基础扎实'],
          improvements: ['需要更多项目经验']
        },
        {
          companyName: '字节跳动',
          jobTitle: 'Web开发工程师',
          score: 92,
          date: '2024-01-10',
          strengths: ['逻辑思维清晰', '学习能力强'],
          improvements: ['算法能力待提升']
        },
        {
          companyName: '腾讯',
          jobTitle: '前端开发',
          score: 78,
          date: '2024-01-05',
          strengths: ['团队协作能力强'],
          improvements: ['技术深度需加强', '表达能力待提升']
        }
      ]
      
      app.globalData.historyRecords = sampleData
      this.setData({
        historyList: sampleData
      })
    } else {
      this.setData({
        historyList: historyRecords
      })
    }
  },
  
  // 查看详情
  viewDetail: function(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.historyList[index]
    
    let detailText = `公司：${item.companyName}\n`
    detailText += `职位：${item.jobTitle}\n`
    detailText += `面试日期：${item.date}\n`
    detailText += `综合评分：${item.score}/100\n\n`
    detailText += `优点：\n${item.strengths.join('\n')}\n\n`
    detailText += `待改进项：\n${item.improvements.join('\n')}`
    
    tt.showModal({
      title: '面试详情',
      content: detailText,
      showCancel: false,
      confirmText: '知道了'
    })
  },
  
  // 跳转到首页开始面试
  goToHome: function() {
    tt.switchTab({
      url: '/pages/index/index'
    })
  }
})
