** ”SmartHire“小程序-需求文档**
**1. 这个小程序是干嘛的？ **

* 这是一个让用户可以查询公司信息及根据这些信息及招聘要求模拟面试的小程序。
* 你可以在里面看到自己输入的对应公司的详细信息，并开始回答模拟面试问题。

**2. 主要功能有哪些？ **

  * **首页：信息输入 (Home / Input Page)**
    * Centered layout. Title 'SmartHire' prominently displayed. Four clear, vertically stacked input fields with labels: '公司名称 (Company Name)','职位名称 (Job Title)',  '招聘要求 (Recruitment Requirements)', '我的名字 (Your Name)'. A prominent, full-width blue button at the bottom labeled '立即开始 (Start Now)'. Professional icons beside each input field。
 * 公司信息锁定页面


  * **公司信息锁定 (Company Info  Lock Page)**
    * Top header with dynamically displayed '为[公司名称]准备面试 (Preparing for [Company Name] Interview)'. Below, a placeholder section for '公司信息 (Company Information)' with 50% opicity black background and a lock icon on it "此处显示模糊文字 (Detailed company culture, business, news will appear here after user payment)". A large, prominent blue button '￥19.9 解锁' at the bottom. A small, encouraging tip section at the top.
    * 用户点击付款按扭出现付款页面

  * **付款页面 (Pay Page)**
    * Top header with dynamically displayed '解锁公司信息及模拟面试问题'. Below, '抖音支付 "支付宝支付“,"微信支付. A large, prominent blue button '￥19.9 解锁' at the bottom. A small, encouraging tip section at the top.
    * 确认付款按扭

  * **公司信息展示 ( Company Info  Page)**
    * Top header with dynamically displayed '为[公司名称]准备面试 (Preparing for [Company Name] Interview)'. Below, a placeholder section for '公司信息 (Company Information)' with sample text like "此处将展示详细的公司文化、主营业务、近期新闻等 (Detailed company culture, business, news will appear here)". A large, prominent blue button '开始模拟面试 (Start Mock Interview)' at the bottom. A small, encouraging tip section at the top..
    * 用户点击付款按扭出现模拟面试页面


  * **模拟面试界面 ( Mock Interview Page)**
    * Top header '模拟面试 (Mock Interview) - 问题 1/5 (Question 1/5)'. A large, clear text area displaying the interview question, e.g., "请您介绍一下您对前端框架Vue.js的理解？(Please describe your understanding of the front-end framework Vue.js?)". Below, a multi-line text input field for user's answer, labeled '输入你的回答 (Type your answer)'. A prominent blue '提交回答 (Submit Answer)' button at the bottom. Clean, focused layout, with minimal distractions.

  * **面试反馈与评分界面 (Feedback & Rating Page)**
    * Top header '面试反馈与评分 (Interview Feedback & Score)'. Prominent, large display of a score, e.g., '综合评分: 85/100 (Overall Score: 85/100)'. Below, two distinct sections: '优点 (Strengths)' and '待改进项 (Areas for Improvement)', each with bullet points or short paragraphs of text. A '保存反馈 (Save Feedback)' button. Further down, a '评价模拟面试效果 (Rate Mock Interview Experience)' section with a 5-star rating component and a text area for user comments. A '提交评价 (Submit Evaluation)' button. Encouraging and constructive tone.

  * **历史反馈界面 (History Page)**
    * Top header '我的面试记录 (My Interview History)'. A scrollable list of interview entries. Each list item clearly displays '公司名称 (Company Name)', '面试日期 (Interview Date)', and '模拟得分 (Mock Score)'. A small icon next to each entry, e.g., a document or star. Clean, organized list, with a simple visual separator between items. If empty, display a centered text '暂无面试记录，快去开启一次模拟面试吧！(No interview history yet, start a mock interview now!)'.
    
    *  

* 

**3. 界面和使用感觉要求**

* **界面样子：**
  * 根据参考规范让小程序的按扭、列表、输入框等看起来风格统一、简洁、好看, （参考：‘https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/design/design-specification/visual-specification/custom-nav-practices').
* **使用感觉：**
   * 用起来要流畅，滑动列表、切换页面不能卡。
   * 点了支付按扭、付款过程和环节，都要有提示，让用户知道现在是什么状态。
* **速度：**
   * 小程序打开速度更快。
   * 每一页加载要快。
* **安全：**
   * 抖音登录及付款过程要安全，不能泄露用户信息。





