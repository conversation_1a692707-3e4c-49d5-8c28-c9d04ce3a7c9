.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
  margin-right: 32rpx;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 32rpx;
  max-width: 768rpx;
  margin: 0 auto;
  width: 100%;

}

.back-button, .help-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: transparent;
  border: none;
  color: #6b7280;
  transition: all 0.2s ease;
}

.back-button:active, .help-button:active {
  background-color: #f3f4f6;
  color: #111418;
}

.back-icon {
  font-size: 48rpx;
  font-weight: bold;
}

.help-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #111418;
}

.content {
  flex: 1;
  max-width: 690rpx;
  margin: 0 auto;
  width: 100%;
  padding: 48rpx 32rpx;


}

.tip-section {
  background-color: #eff6ff;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 48rpx;
}

.tip-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1d4ed8;
  text-align: center;
}

.info-section {
  margin-bottom: 32rpx;

}

.section-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #111418;
  margin-bottom: 24rpx;
}

.info-content {
  max-width: 690rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  background-color: #f9fafb;
  padding: 32rpx;
 

}

.info-text {
  font-size: 32rpx;
  color: #374151;
  line-height: 1.6;

}

.footer {
  position: sticky;
  bottom: 0;
  background-color: #ffffff;
  padding: 32rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  max-width: 690rpx;
  margin: 0 auto;
  width: 100%;

}

.start-interview-button {
  width: 100%;
  height: 112rpx;
  background-color: #1383eb;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 56rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(19, 131, 235, 0.3);
  transition: all 0.2s ease;
  line-height: 112rpx;
}
.start-interview-button:active {
  background-color: #1e40af;
  transform: translateY(2rpx);

}
