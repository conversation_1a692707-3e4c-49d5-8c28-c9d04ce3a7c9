// 测试签名生成
import { generateDouyinPaySign } from './utils/douyinPaySign.js';

// 测试参数 - 使用文档中的示例
const testParams = {
    app_id: "tt07e3715e98c9aac0",
    out_order_no: "out_order_no_1",
    total_amount: 12800,
    subject: "测试商品",
    body: "测试商品",
    valid_time: 180,
    notify_url: "https://api.iiyyeixin.com/Notify/bytedancePay"
};

console.log('测试参数:', testParams);

try {
    const signature = generateDouyinPaySign(testParams);
    console.log('生成的签名:', signature);
    
    // 验证签名长度和格式
    if (signature && signature.length > 0) {
        console.log('✅ 签名生成成功');
        console.log('签名长度:', signature.length);
    } else {
        console.log('❌ 签名生成失败');
    }
} catch (error) {
    console.error('❌ 签名生成异常:', error.message);
}
