.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.payment-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 40rpx;
  text-align: center;
}

.pay-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-color: #f59e0b;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.3);
}

.unlock-icon {
  font-size: 60rpx;
  color: #ffffff;
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  color: #111418;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.service-info {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 100%;
  padding: 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.service-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #111418;
  margin-bottom: 8rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #6b7280;
}

.service-price {
  font-size: 40rpx;
  font-weight: bold;
  color: #f59e0b;
}

.payment-methods {
  width: 100%;
  margin-bottom: 80rpx;
}

.method-container {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.payment-method:last-child {
  border-bottom: none;
}

.payment-method.selected {
  background-color: #f0f9ff;
}

.method-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  color: #4b5563;
}

.method-name {
  flex: 1;
  font-size: 32rpx;
  color: #111418;
}

.check-icon {
  font-size: 40rpx;
  color: #d1d5db;
  transition: color 0.2s ease;
}

.check-icon.checked {
  color: #f59e0b;
}

.pay-button {
  width: 100%;
  height: 112rpx;
  background-color: #f59e0b;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 60rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.3);
  margin-bottom: 32rpx;
  transition: all 0.2s ease;
}

.pay-button:active {
  background-color: #d97706;
  transform: translateY(2rpx);
}

.agreement {
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.5;
}
