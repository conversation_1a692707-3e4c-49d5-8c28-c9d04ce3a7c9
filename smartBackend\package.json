{"name": "smarthire-backend-project", "version": "1.0.0", "description": "SmartHire Mini App Backend", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "init-db": "node init-db.js", "test-db": "node test-db.js", "test-sign": "node test-sign.js"}, "dependencies": {"axios": "^1.9.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "pg": "^8.16.0", "uuid": "^11.1.0"}}