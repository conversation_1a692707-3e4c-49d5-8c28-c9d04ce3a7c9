<view class="container">
  <view class="header">
    <button class="back-button" bindtap="goBack">
      <text class="back-icon">‹</text>
    </button>
    <text class="header-title">面试反馈与评分</text>
    <view class="placeholder"></view>
  </view>
  
  <view class="content">
    <view class="score-section">
      <text class="score-title">综合评分</text>
      <view class="score-display">
        <text class="score-number">{{score}}</text>
        <text class="score-total">/100</text>
      </view>
    </view>
    
    <view class="feedback-section">
      <text class="section-title">优点</text>
      <view class="feedback-content">
        <view class="feedback-item" wx:for="{{strengths}}" wx:key="index">
          <text class="bullet">•</text>
          <text class="feedback-text">{{item}}</text>
        </view>
      </view>
    </view>
    
    <view class="feedback-section">
      <text class="section-title">待改进项</text>
      <view class="feedback-content">
        <view class="feedback-item" wx:for="{{improvements}}" wx:key="index">
          <text class="bullet">•</text>
          <text class="feedback-text">{{item}}</text>
        </view>
      </view>
    </view>
    
    <button class="save-button" bindtap="saveFeedback">
      保存反馈
    </button>
    
    <view class="divider"></view>
    
    <view class="rating-section">
      <text class="section-title">评价模拟面试效果</text>
      <view class="stars-container">
        <view class="star {{index < rating ? 'filled' : ''}}" 
              wx:for="{{5}}" 
              wx:key="index" 
              bindtap="setRating" 
              data-rating="{{index + 1}}">
          <text class="star-icon">★</text>
        </view>
      </view>
      <textarea 
        class="comment-input" 
        placeholder="请输入您的评价 (选填)"
        value="{{comment}}"
        bindinput="onCommentInput"
        maxlength="200"
      ></textarea>
    </view>
    
    <button class="submit-button" bindtap="submitEvaluation">
      提交评价
    </button>
  </view>
</view>
