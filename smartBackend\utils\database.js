// backend/utils/database.js

import { Pool } from 'pg'; // 引入 pg 库的 Pool 类

// 从环境变量中获取数据库连接URL  
                                          
const connectionString = "postgresql://postgres.rwivryeayesqkmbodxna:<EMAIL>:5432/postgres";

// 检查环境变量是否存在
if (!connectionString) {
  console.error('DATABASE_URL environment variable is not set. Please check your .env file or environment configuration.');
  throw new Error('DATABASE_URL environment variable is not set.'); // 抛出错误终止应用启动
}

// 创建一个 PostgreSQL 连接池 (Pool)
const pool = new Pool({
  connectionString: connectionString,
  // 可选配置：
  // max: 20,
  // idleTimeoutMillis: 30000,
  // connectionTimeoutMillis: 2000,
});

// 监听连接池的错误事件
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
});

/**
 * 封装一个通用的数据库查询函数。
 * 允许执行任何 SQL 查询，并传递参数以防止 SQL 注入。
 *
 * @param {string} text - SQL 查询字符串。
 * @param {Array<any>} params - 查询参数数组。
 * @returns {Promise<any>} - 查询结果。
 */
async function query(text, params) {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query:', { text, duration, rows: res.rowCount });
    return res.rows;
  } catch (error) {
    console.error('Error executing query:', { text, params, error });
    throw error;
  }
}

/**
 * 获取一个客户端连接，用于事务。
 * 当需要执行多个相关的SQL操作，并且这些操作必须要么全部成功，要么全部失败时，使用事务。
 *
 * @returns {Promise<import('pg').PoolClient>} - 数据库客户端实例。
 */
async function getClient() { // <-- 确保这个函数名称是 getClient
  const client = await pool.connect();
  return client;
}

// 重点：使用 ES Modules 的命名导出
export {
  query,     // <-- 导出 query 函数
  getClient, // <-- 导出 getClient 函数
  pool       // 导出 pool 实例，方便更高级的用法
};