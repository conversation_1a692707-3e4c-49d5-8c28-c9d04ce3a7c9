<view class="container">
  <view class="header">
    <button class="invisible-button"></button>
    <text class="header-title">模拟面试</text>
    <button class="help-button" bindtap="showHelp">
      <text class="help-icon">?</text>
    </button>
  </view>
  
  <view class="content">
    <view class="question-info">
      <text class="question-number">问题 {{currentQuestion}}/{{totalQuestions}}</text>
      <text class="question-text">{{questionText}}</text>
    </view>
    
    <view class="answer-section">
      <text class="answer-label">输入你的回答</text>
      <textarea 
        class="answer-input" 
        placeholder="请在此处输入您的答案..."
        value="{{userAnswer}}"
        bindinput="onAnswerInput"
        maxlength="1000"
      ></textarea>
      <view class="char-count">
        <text class="count-text">{{userAnswer.length}}/1000</text>
      </view>
    </view>
  </view>
  
  <view class="footer">
 
    <button class="submit-button" bindtap="submitAnswer" >
      {{isLastQuestion ? '完成面试' : '提交回答'}}
    </button>
  </view>
</view>
