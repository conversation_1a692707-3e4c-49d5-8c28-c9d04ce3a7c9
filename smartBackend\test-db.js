// 测试数据库连接和表结构
import { query } from './utils/database.js';

async function testDatabase() {
    try {
        console.log('🔍 测试数据库连接...');
        
        // 1. 测试基本连接
        const result = await query('SELECT NOW() as current_time', []);
        console.log('✅ 数据库连接成功，当前时间:', result[0].current_time);
        
        // 2. 检查表是否存在
        const tables = await query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('users', 'orders', 'company_info', 'interview_records')
            ORDER BY table_name
        `, []);
        
        console.log('\n📋 数据库表状态:');
        const expectedTables = ['users', 'orders', 'company_info', 'interview_records'];
        const existingTables = tables.map(t => t.table_name);
        
        expectedTables.forEach(tableName => {
            if (existingTables.includes(tableName)) {
                console.log(`  ✅ ${tableName} - 存在`);
            } else {
                console.log(`  ❌ ${tableName} - 不存在`);
            }
        });
        
        // 3. 检查orders表结构
        if (existingTables.includes('orders')) {
            const columns = await query(`
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'orders' 
                ORDER BY ordinal_position
            `, []);
            
            console.log('\n📊 orders表结构:');
            columns.forEach(col => {
                console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
            });
        }
        
        // 4. 测试插入操作
        console.log('\n🧪 测试插入操作...');
        const testUser = await query(
            'INSERT INTO users (douyin_openid) VALUES ($1) RETURNING id',
            [`test_${Date.now()}`]
        );
        
        if (testUser && testUser.length > 0) {
            console.log('✅ 用户插入测试成功，返回ID:', testUser[0].id);
            
            // 清理测试数据
            await query('DELETE FROM users WHERE id = $1', [testUser[0].id]);
            console.log('🧹 测试数据已清理');
        } else {
            console.log('❌ 用户插入测试失败');
        }
        
    } catch (error) {
        console.error('❌ 数据库测试失败:', error);
        console.error('错误详情:', error.message);
    }
}

// 运行测试
testDatabase();
