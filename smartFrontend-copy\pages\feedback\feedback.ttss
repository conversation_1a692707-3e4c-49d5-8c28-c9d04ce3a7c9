.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 32rpx;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: transparent;
  border: none;
  color: #6b7280;
  transition: all 0.2s ease;
}

.back-button:active {
  background-color: #f3f4f6;
}

.back-icon {
  font-size: 48rpx;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #121516;
}

.placeholder {
  width: 80rpx;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

.score-section {
  text-align: center;
  padding: 48rpx;
  background-color: #f8fafc;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.score-title {
  font-size: 60rpx;
  font-weight: bold;
  color: #121516;
  margin-bottom: 16rpx;
  display: block;
}

.score-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-top: 16rpx;
}

.score-number {
  font-size: 120rpx;
  font-weight: bold;
  color: #c5d9eb;
}

.score-total {
  font-size: 60rpx;
  color: #6b7280;
  margin-left: 8rpx;
}

.feedback-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #121516;
  margin-bottom: 24rpx;
  display: block;
}

.feedback-content {
  background-color: #f8fafc;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.feedback-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.feedback-item:last-child {
  margin-bottom: 0;
}

.bullet {
  font-size: 32rpx;
  color: #6b7280;
  margin-right: 16rpx;
  line-height: 1.5;
}

.feedback-text {
  flex: 1;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
}

.save-button {
  width: 100%;
  height: 96rpx;
  background-color: #c5d9eb;
  color: #374151;
  font-size: 36rpx;
  font-weight: 600;
  border-radius: 24rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(197, 217, 235, 0.3);
  transition: all 0.2s ease;
}

.save-button:active {
  background-color: #a7c7e0;
  transform: translateY(2rpx);
}

.divider {
  height: 1rpx;
  background-color: #e5e7eb;
  margin: 64rpx 0;
}

.rating-section {
  margin-bottom: 32rpx;
}

.stars-container {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  margin: 32rpx 0;
}

.star {
  transition: all 0.2s ease;
}

.star-icon {
  font-size: 72rpx;
  color: #d1d5db;
  transition: color 0.2s ease;
}

.star.filled .star-icon {
  color: #fbbf24;
}

.comment-input {
  width: 100%;
  min-height: 240rpx;
  border: 1rpx solid #d1d5db;
  border-radius: 24rpx;
  background-color: #f8fafc;
  padding: 32rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  margin-top: 32rpx;
}

.comment-input:focus {
  border-color: #c5d9eb;
  outline: none;
}

.comment-input::placeholder {
  color: #9ca3af;
}

.submit-button {
  width: 100%;
  height: 96rpx;
  background-color: #c5d9eb;
  color: #374151;
  font-size: 36rpx;
  font-weight: 600;
  border-radius: 24rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(197, 217, 235, 0.3);
  margin-bottom: 48rpx;
  transition: all 0.2s ease;
}

.submit-button:active {
  background-color: #a7c7e0;
  transform: translateY(2rpx);
}
