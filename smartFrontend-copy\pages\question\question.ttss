.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F8F9FA;
}

.header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.invisible-button {
  width: 80rpx;
  height: 80rpx;
  background: transparent;
  border: none;
  visibility: hidden;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #111518;
  text-align: center;
  flex: 1;
}

.help-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: transparent;
  border: none;
  color: #617689;
  transition: all 0.2s ease;
}

.help-button:active {
  background-color: #f3f4f6;
  color: #1383eb;
}

.help-icon {
  font-size: 40rpx;
  font-weight: bold;
}

.content {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 48rpx;

}

.question-info {
  text-align: center;
}

.question-number {
  font-size: 40rpx;
  font-weight: 600;
  color: #111518;
  margin-bottom: 8rpx;
  display: block;
}

.question-text {
  font-size: 28rpx;
  color: #617689;
  line-height: 1.5;
}

.answer-section {
  background-color: #ffffff;
  padding: 48rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.answer-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #344054;
  margin-bottom: 16rpx;
}

.answer-input {
  flex: 1;
  min-height: 400rpx;
  border: 1rpx solid #D0D5DD;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 32rpx;
  color: #111518;
  background-color: #ffffff;
  line-height: 1.5;
  resize: none;
  transition: border-color 0.2s ease;

}

.answer-input:focus {
  border-color: #1383eb;
  outline: none;
}

.answer-input::placeholder {
  color: #98A2B3;
}

.char-count {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.count-text {
  font-size: 24rpx;
  color: #98A2B3;
}

.footer {
  position: sticky;
  bottom: 0;
  background-color: #ffffff;
  padding: 32rpx;
  border-top: 1rpx solid #EAECF0;
}

.submit-button {
  width: 100%;
  height: 96rpx;
  background-color: #1383eb;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 16rpx;
  border: none;
  transition: all 0.2s ease;
  line-height: 96rpx;
}

.submit-button:active {
  background-color: #0F6FD2;
  transform: translateY(2rpx);
}

.submit-button:disabled {
  background-color: #D0D5DD;
  color: #98A2B3;
  transform: none;
}
