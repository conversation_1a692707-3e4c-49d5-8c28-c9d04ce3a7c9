# 抖音支付配置指南

## 问题描述
遇到抖音支付签名校验异常错误：
```
{ err_no: 2008, err_tips: '签名校验异常，请使用正确的签名', data: null }
```

## 解决方案

### 1. 检查抖音开放平台配置

1. 登录 [抖音开放平台](https://developer.open-douyin.com/)
2. 进入您的小程序应用
3. 在"开发管理" -> "支付配置"中：
   - 确认支付功能已开通
   - 检查商户号是否正确
   - 确认RSA公钥已正确上传

### 2. 生成RSA密钥对

如果还没有RSA密钥对，请按以下步骤生成：

```bash
# 生成私钥
openssl genrsa -out private_key.pem 2048

# 生成公钥
openssl rsa -in private_key.pem -pubout -out public_key.pem

# 查看私钥内容（用于配置到服务器）
cat private_key.pem

# 查看公钥内容（用于上传到抖音开放平台）
cat public_key.pem
```

### 3. 配置环境变量

1. 复制 `.env.example` 为 `.env`
2. 填入正确的配置信息：

```env
# 抖音小程序配置
DOUYIN_APP_ID=你的小程序APPID
DOUYIN_APP_SECRET=你的小程序密钥

# 抖音支付配置
DOUYIN_APP_ID_PAY=你的支付APPID（通常与小程序APPID相同）
DOUYIN_MERCHANT_ID=你的商户号

# RSA私钥（完整内容，包含BEGIN和END标记）
DOUYIN_RSA_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
你的完整私钥内容
-----END RSA PRIVATE KEY-----"
```

### 4. 验证签名生成

运行测试脚本验证签名是否正确生成：

```bash
cd smartBackend
node test-sign.js
```

### 5. 常见问题排查

#### 问题1：私钥格式错误
- 确保私钥包含完整的 `-----BEGIN RSA PRIVATE KEY-----` 和 `-----END RSA PRIVATE KEY-----`
- 确保私钥内容没有额外的空格或换行符
- 在环境变量中使用双引号包围私钥

#### 问题2：参数不匹配
- 检查 `app_id` 是否与抖音开放平台配置一致
- 确认 `out_order_no` 在同一app_id下唯一
- 验证 `total_amount` 单位为分（例如：1元 = 100分）

#### 问题3：签名算法错误
- 确认使用 RSA-SHA256 算法
- 参数按ASCII字典序排序
- 排除空值和sign字段
- 使用UTF-8编码

### 6. 调试技巧

1. 开启详细日志：
   ```javascript
   console.log('待签名字符串:', stringA);
   console.log('生成的签名:', sign);
   ```

2. 对比官方示例：
   - 使用文档中的示例参数
   - 验证生成的签名字符串格式
   - 确认签名长度和格式

3. 检查网络请求：
   ```javascript
   console.log('请求参数:', paramsForDouyin);
   console.log('抖音API响应:', douyinPayRes.data);
   ```

### 7. 测试流程

1. 使用沙盒环境测试：
   ```
   https://open-sandbox.douyin.com/api/apps/ecpay/v1/create_order
   ```

2. 确认沙盒环境配置正确后，切换到正式环境：
   ```
   https://developer.toutiao.com/api/apps/ecpay/v1/create_order
   ```

### 8. 联系支持

如果问题仍然存在，请：
1. 收集完整的错误日志
2. 记录请求参数和响应
3. 联系抖音开放平台技术支持

## 相关文档

- [抖音支付开发文档](https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/payment/)
- [RSA签名算法说明](https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/open-capacity/guaranteed-payment/TE/#_%E4%B8%89%E7%AD%BE%E5%90%8D-demo)
