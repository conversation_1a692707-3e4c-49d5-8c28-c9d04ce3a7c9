// backend/utils/douyinPaySign.js

import crypto from 'crypto'; // 使用 ES Modules 风格导入 crypto 模块

// 注意：这里使用的是示例私钥，实际使用时请替换为您在抖音开放平台配置的真实私钥
// 您需要在抖音开放平台生成RSA密钥对，并将公钥上传到平台，私钥保存在服务器
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
const DOUYIN_API_KEY = process.env.DOUYIN_API_KEY; // 用于前端 tt.requestPayment 的MD5签名（如果需要）

if (!DOUYIN_RSA_PRIVATE_KEY) {
  console.error('DOUYIN_RSA_PRIVATE_KEY environment variable is not set. Please check your .env file.');
  throw new Error('DOUYIN_RSA_PRIVATE_KEY is missing.');
}

// 签名函数：用于后端调用抖音支付接口时的签名 (RSA-SHA256)
function generateDouyinPaySign(params) {
    console.log('开始生成签名，参数:', params);

    // 1. 过滤空值和sign字段，确保所有值都转为字符串
    const filteredParams = {};
    for (const key in params) {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '' && key !== 'sign') {
            // 确保所有参数值都是字符串类型
            filteredParams[key] = String(params[key]);
        }
    }

    console.log('过滤后的参数:', filteredParams);

    // 2. 参数名ASCII字典序排序
    const sortedKeys = Object.keys(filteredParams).sort();
    console.log('排序后的键:', sortedKeys);

    // 3. 拼接成"key1=value1&key2=value2"格式
    let stringA = '';
    for (const key of sortedKeys) {
        stringA += `${key}=${filteredParams[key]}&`;
    }
    stringA = stringA.slice(0, -1); // 移除最后一个&

    console.log('待签名字符串:', stringA);

    // 4. RSA SHA256 签名 (使用您的私钥)
    try {
        const sign = crypto.createSign('RSA-SHA256')
            .update(stringA, 'utf8')
            .sign(DOUYIN_RSA_PRIVATE_KEY, 'base64'); // base64编码

        console.log('生成的签名:', sign);
        return sign;
    } catch (error) {
        console.error('Error during RSA-SHA256 signing:', error);
        console.error('Private key format check:', DOUYIN_RSA_PRIVATE_KEY.substring(0, 50) + '...');
        throw new Error('Failed to generate Douyin Pay signature.');
    }
}

// 签名函数：用于前端 tt.requestPayment 的签名 (MD5)，如果后端需要为前端生成这个签名
// 抖音支付文档中，tt.requestPayment 的 orderInfo.sign 字段是针对其内部参数（如 order_id, app_id, merchant_id, timestamp, nonce_str）
// 使用 MD5 签名算法，并加上 API_KEY。请核对最新抖音文档。
function generateDouyinPaySignForFrontend(params) {
    if (!DOUYIN_API_KEY) {
        console.warn('DOUYIN_API_KEY is not set. Frontend signature might fail.');
        throw new Error('DOUYIN_API_KEY is missing for frontend signature.');
    }
    // 1. 过滤空值和sign字段
    const filteredParams = {};
    for (const key in params) {
        if (params[key] !== null && params[key] !== undefined && key !== 'sign') {
            filteredParams[key] = params[key];
        }
    }

    // 2. 参数名ASCII字典序排序
    const sortedKeys = Object.keys(filteredParams).sort();

    // 3. 拼接成"key1=value1&key2=value2"格式
    let stringA = '';
    for (const key of sortedKeys) {
        stringA += `${key}=${filteredParams[key]}&`;
    }
    stringA = stringA.slice(0, -1); // 移除最后一个&

    // 4. 拼接 API_KEY
    const stringSignTemp = `${stringA}&key=${DOUYIN_API_KEY}`;

    // 5. MD5加密，转大写
    const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();
    return sign;
}

// 核心：使用 ES Modules 的命名导出
export {
  generateDouyinPaySign,
  generateDouyinPaySignForFrontend // 确保这个函数也在这里被导出，如果需要的话
};