const app = getApp()

Page({
  data: {
    selectedMethod: 'wechat' // 默认选择微信支付
  },
  
  onLoad: function () {
    console.log('支付页面加载完成')
  },
  
  // 选择支付方式
  selectMethod: function(e) {
    const method = e.currentTarget.dataset.method
    this.setData({
      selectedMethod: method
    })
  },
  
  // 支付按钮点击事件
  onPay: function() {
    const { selectedMethod } = this.data
    
    // 显示支付加载状态
    tt.showLoading({
      title: '正在支付...'
    })
    
    // 模拟支付过程
    setTimeout(() => {
      tt.hideLoading()
      
      // 支付成功提示
      tt.showToast({
        title: '支付成功！',
        icon: 'success',
        duration: 2000
      })
      
      // 设置支付状态
      app.globalData.isPaid = true
      
      // 延迟跳转到公司信息页面
      setTimeout(() => {
        tt.redirectTo({
          url: '/pages/companyinfo/companyinfo'
        })
      }, 2000)
      
    }, 1500)
  }
})
