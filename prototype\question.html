<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    body {
      font-family: 'Manrope', 'Noto Sans', sans-serif;
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-[#F8F9FA]">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden">
<div class="flex-grow">
<header class="sticky top-0 z-10 bg-white shadow-sm">
<div class="flex items-center justify-between p-4">
<button class="invisible p-2">
<svg fill="none" height="24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
<polyline points="15 18 9 12 15 6"></polyline>
</svg>
</button>
<h1 class="text-lg font-bold text-center text-[#111518] flex-1">模拟面试</h1>
<button class="p-2 text-[#617689] hover:text-[#1383eb]">
<svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
<path d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
</svg>
</button>
</div>
</header>
<main class="p-6 space-y-6">
<div class="text-center">
<h2 class="text-xl font-semibold text-[#111518] mb-1">问题 1/5</h2>
<p class="text-sm text-[#617689]">请您介绍一下您对前端框架Vue.js的理解？</p>
</div>
<div class="bg-white p-6 rounded-xl shadow-lg">
<label class="block text-sm font-medium text-[#344054] mb-2" for="user-answer">输入你的回答</label>
<textarea class="form-textarea block w-full min-h-[200px] resize-y overflow-hidden rounded-lg border-[#D0D5DD] focus:border-[#1383eb] focus:ring-[#1383eb] focus:ring-opacity-50 p-3 text-base text-[#111518] placeholder:text-[#98A2B3] shadow-sm transition-colors duration-150" id="user-answer" placeholder="请在此处输入您的答案..."></textarea>
</div>
</main>
</div>
<footer class="sticky bottom-0 bg-white p-4 border-t border-[#EAECF0]">
<button class="w-full flex items-center justify-center rounded-lg h-12 px-5 bg-[#1383eb] text-white text-base font-bold leading-normal tracking-[0.015em] hover:bg-[#0F6FD2] focus:outline-none focus:ring-2 focus:ring-[#1383eb] focus:ring-offset-2 transition-colors duration-150">
<span class="truncate">提交回答</span>
</button>
</footer>
</div>

</body></html>