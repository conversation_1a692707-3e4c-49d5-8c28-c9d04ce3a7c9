// backend/routes/company.js
import express from 'express';
const router = express.Router();
import axios from 'axios';
import { query, getClient } from '../utils/database.js'; // <-- 使用命名导入

const SHOWAPI_APP_KEY = process.env.SHOWAPI_APP_KEY;

// 独立函数，可被支付回调和前端直接调用
async function getCompanyInfoFromShowAPI(companyName) {
    // 1. 从缓存中查询
    let cacheData = await db.query('SELECT data, last_updated FROM company_info_cache WHERE company_name = ?', [companyName]);

    if (cacheData.length > 0) {
        const cachedInfo = cacheData[0];
        // 简单判断缓存是否过期 (例如24小时)
        const cacheExpiry = new Date(cachedInfo.last_updated);
        cacheExpiry.setHours(cacheExpiry.getHours() + 24); // 假设缓存有效期24小时

        if (new Date() < cacheExpiry) {
            console.log(`公司信息 "${companyName}" 已从缓存获取。`);
            return cachedInfo.data; // 返回缓存的数据
        } else {
            console.log(`公司信息 "${companyName}" 缓存已过期，将重新获取。`);
        }
    }

    // 2. 如果未缓存或已过期，调用 showapi 接口
    try {
        const showApiUrl = `https://route.showapi.com/1476-1?appKey=${SHOWAPI_APP_KEY}&keyword=${encodeURIComponent(companyName)}`;
        const response = await axios.get(showApiUrl);

        if (response.data && response.data.showapi_res_code === 0) {
            const companyData = response.data.showapi_res_body;
            // 3. 将获取到的数据存入或更新数据库缓存
            if (cacheData.length > 0) {
                await db.query('UPDATE company_info_cache SET data = ?, last_updated = CURRENT_TIMESTAMP WHERE company_name = ?',
                               [JSON.stringify(companyData), companyName]);
            } else {
                await db.query('INSERT INTO company_info_cache (company_name, data) VALUES (?, ?)',
                               [companyName, JSON.stringify(companyData)]);
            }
            console.log(`公司信息 "${companyName}" 已通过 showapi 获取并缓存。`);
            return companyData;
        } else {
            console.error('ShowAPI接口返回错误:', response.data);
            throw new Error(response.data.showapi_res_error || '获取公司信息失败');
        }
    } catch (error) {
        console.error('调用ShowAPI接口失败:', error);
        throw new Error('获取公司信息失败或网络问题');
    }
}

// 提供给前端的接口
router.get('/getInfo', async (req, res) => {
    const { companyName } = req.query;
    if (!companyName) {
        return res.status(400).json({ code: -1, msg: '公司名称不能为空' });
    }

    try {
        const companyInfo = await getCompanyInfoFromShowAPI(companyName);
        res.json({ code: 0, msg: '获取公司信息成功', data: companyInfo });
    } catch (error) {
        res.status(500).json({ code: -1, msg: error.message || '获取公司信息失败' });
    }
});

export default router;