const app = getApp()

Page({
  data: {
    companyName: ''
  },
  
  onLoad: function () {
    // 获取全局用户信息
    const userInfo = app.globalData.userInfo
    if (userInfo && userInfo.companyName) {
      this.setData({
        companyName: userInfo.companyName
      })
    }
    console.log('公司信息锁定页面加载完成')
  },
  
  // 解锁按钮点击事件
  onUnlock: function() {
    // 跳转到支付页面
    tt.navigateTo({
      url: '/pages/pay/pay'
    })
  }
})
