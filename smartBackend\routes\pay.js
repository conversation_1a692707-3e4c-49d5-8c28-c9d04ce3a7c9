// backend/routes/pay.js (续)
import express from 'express';
import axios from 'axios';
import { query, getClient } from '../utils/database.js'; // 数据库模块已修正


import { generateDouyinPaySign } from '../utils/douyinPaySign.js'; // 假设封装了签名逻辑
import  { v4 as uuidv4 }  from 'uuid'; // 生成唯一ID

const router = express.Router();

const DOUYIN_MERCHANT_ID = "75061188496083049470";
const DOUYIN_APP_ID_PAY = "tt04c2727f9bcae58501"; // 支付侧的APPID，可能与小程序APPID不同
const DOUYIN_NOTIFY_URL = "https://01e6-240e-343-5849-4f00-4981-edb6-3ce0-db1a.ngrok-free.app/api/pay/notify"; // 支付回调地址

router.post('/createOrder', async (req, res) => {
    const { userId, openId, amount, productDescription, companyName, recruitmentRequirements, userName } = req.body;

    // 1. 生成内部订单号
    const order_sn = `SHIRE${Date.now()}${Math.floor(Math.random() * 1000)}`;
    const total_amount_fen = Math.round(amount * 100); // 抖音支付金额单位为分

    try {
        // 2. 将订单信息存入数据库，状态为PENDING
        const orderInsertRes = await  query(
            'INSERT INTO orders (user_id, order_sn, amount, product_description, company_name, recruitment_requirements, user_name, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
            [userId, order_sn, amount, productDescription, companyName, recruitmentRequirements, userName, 'PENDING']
        );
        const orderId = orderInsertRes.insertId;

        // 3. 准备抖音支付参数
        const paramsForDouyin = {
            app_id: DOUYIN_APP_ID_PAY,
            merchant_id: DOUYIN_MERCHANT_ID,
            out_order_no: order_sn, // 您的内部订单号
            total_amount: total_amount_fen,
            subject: productDescription,
            body: `为 ${companyName} 定制模拟面试`, // 商品详情
             valid_time: 300,
            trade_type: 'H5', // 小程序支付通常用H5或APP
            version: '2.0',
            notify_url: DOUYIN_NOTIFY_URL,
            timestamp: Date.now().toString(),
            nonce_str: uuidv4().replace(/-/g, ''), // 随机字符串
            customer_id: openId, // 用户OpenID
            // ... 其他可选参数，如小程序路径、支付回调参数等
        };

        // 4. 对参数进行签名
        const sign = generateDouyinPaySign(paramsForDouyin); // 假设签名函数已实现
        paramsForDouyin.sign = sign;

        // 5. 调用抖音支付统一支付接口             
        const douyinPayRes = await axios.post('https://developer.toutiao.com/api/apps/ecpay/v1/create_order', paramsForDouyin, {
            headers: { 'Content-Type': 'application/json' }
        });

        if (douyinPayRes.data.err_no !== 0) {
            console.error('抖音支付创建订单失败:', douyinPayRes.data);
            // 订单状态标记为FAILED
            await  query('UPDATE orders SET status = $1 WHERE id = $2', ['FAILED', orderId]);
            return res.status(500).json({ code: -1, msg: douyinPayRes.data.err_tips || '抖音支付创建订单失败' });
        }

        const { prepay_id, order_info } = douyinPayRes.data.data;
        // 更新订单表中的 prepay_id
        await  query('UPDATE orders SET prepay_id = $1, douyin_order_id = $2 WHERE id = $3', [prepay_id, douyinPayRes.data.data.order_id, orderId]);

      // 直接将抖音返回的 order_info 传递给前端
        res.json({
            code: 0,
            msg: '订单创建成功',
            data: {
                orderInfo: order_info // <-- 确保这里是抖音返回的完整的 order_info
            }
        });





   

    } catch (error) {
        console.error('创建订单失败:', error);
        res.status(500).json({ code: -1, msg: '服务器错误' });
    }
});

export default router;