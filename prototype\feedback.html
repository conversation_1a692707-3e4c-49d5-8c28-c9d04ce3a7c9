<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-white">
<div class="relative flex size-full min-h-screen flex-col" style='font-family: Manrope, "Noto Sans", sans-serif;'>
<header class="sticky top-0 z-10 bg-white shadow-sm">
<div class="flex items-center p-4">
<button class="flex h-10 w-10 items-center justify-center rounded-full text-gray-600 hover:bg-gray-100">
<span class="material-icons text-2xl">arrow_back_ios_new</span>
</button>
<h1 class="flex-1 text-center text-xl font-bold text-[#121516]">面试反馈与评分</h1>
<div class="w-10"></div>
</div>
</header>
<main class="flex-1 overflow-y-auto p-6 space-y-6">
<section class="text-center py-6 bg-slate-50 rounded-xl shadow-sm">
<h2 class="text-3xl font-bold text-[#121516]">综合评分</h2>
<p class="text-6xl font-bold text-[#c5d9eb] mt-2">85<span class="text-3xl text-gray-500">/100</span></p>
</section>
<section>
<h3 class="text-xl font-semibold text-[#121516] mb-3">优点</h3>
<div class="bg-slate-50 p-4 rounded-lg shadow-sm">
<ul class="list-disc list-inside space-y-2 text-gray-700">
<li>沟通能力出色，回答清晰简洁。</li>
<li>对职位要求有深刻理解。</li>
<li>展现出积极主动的学习态度。</li>
</ul>
</div>
</section>
<section>
<h3 class="text-xl font-semibold text-[#121516] mb-3">待改进项</h3>
<div class="bg-slate-50 p-4 rounded-lg shadow-sm">
<ul class="list-disc list-inside space-y-2 text-gray-700">
<li>可以提供更具体的例子来支持论述。</li>
<li>考虑详细阐述过往经历以更有效地展示能力。</li>
</ul>
</div>
</section>
<button class="w-full rounded-xl bg-[#c5d9eb] py-3 text-lg font-semibold text-gray-800 shadow-md hover:bg-opacity-90 transition-colors">
          保存反馈
        </button>
<hr class="border-gray-200 my-8"/>
<section>
<h3 class="text-xl font-semibold text-[#121516] mb-4">评价模拟面试效果</h3>
<div class="flex justify-center space-x-2 mb-4">
            {[...Array(5)].map((_, i) =&gt; (
              <button class="text-4xl text-gray-300 hover:text-yellow-400 transition-colors" key="{i}">
<span class="material-icons" style="font-size: 36px">star_border</span>
</button>
            ))}
          </div>
<textarea class="form-textarea w-full rounded-xl border border-gray-300 bg-slate-50 p-4 text-gray-700 focus:border-[#c5d9eb] focus:ring-1 focus:ring-[#c5d9eb] min-h-[120px] resize-none placeholder-gray-500" placeholder="请输入您的评价 (选填)"></textarea>
</section>
<button class="w-full rounded-xl bg-[#c5d9eb] py-3 text-lg font-semibold text-gray-800 shadow-md hover:bg-opacity-90 transition-colors mb-6">
          提交评价
        </button>
</main>
</div>

</body></html>