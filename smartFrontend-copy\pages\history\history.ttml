<view class="container">
  <view class="header">
    <text class="header-title">我的面试记录</text>
  </view>
  
  <view class="content">
    <view wx:if="{{historyList.length > 0}}" class="history-list">
      <view class="history-item" 
            wx:for="{{historyList}}" 
            wx:key="index"
            bindtap="viewDetail"
            data-index="{{index}}">
        <view class="item-icon">
          <text class="icon-text">📄</text>
        </view>
        <view class="item-content">
          <text class="company-name">{{item.companyName}}</text>
          <text class="interview-date">{{item.date}}</text>
        </view>
        <view class="item-score">
          <text class="score-text">{{item.score}}</text>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无面试记录，快去开启一次模拟面试吧！</text>
      <button class="start-interview-button" bindtap="goToHome">
        开始面试
      </button>
    </view>
  </view>
</view>
