const app = getApp()

Page({
  data: {
    user: null,
    openid: '', // 新增：保存 openid
    companyName: '',
    jobTitle: '',
    requirements: '',
    userName: ''
  },

  onLoad: function () {
    console.log('SmartHire - 首页加载完成')
  },
 // 新增：获取 openid 方法
   getOpenId() {
    const that = this;
    tt.login({
      success(res) {
        const code = res.code;
        // 调用你自己的后端
        tt.request({
          url: 'https://23c1-240e-343-5849-4f00-6812-d161-3dbb-23fc.ngrok-free.app/api/code2session', // 👈 填你的后端接口地址
          method: 'POST',
          data: {
            code: code
          },
          success(result) {
            console.log('后端返回内容:', result); // 👈 先打印看清楚结构
            const openid = result.data.openid;
            console.log('openid:', openid);
            that.setData({ openid });
          },
          fail(err) {
            console.error('换取 openid 失败', err);
          }
        });
      },
      fail(err) {
        console.error('tt.login 登录失败:', err);
      }
    });
  }
  ,
  // 输入事件处理
  onCompanyNameInput: function(e) {
    this.setData({
      companyName: e.detail.value
    })
  },

  onJobTitleInput: function(e) {
    this.setData({
      jobTitle: e.detail.value
    })
  },

  onRequirementsInput: function(e) {
    this.setData({
      requirements: e.detail.value
    })
  },

  onUserNameInput: function(e) {
    this.setData({
      userName: e.detail.value
    })
  },

  // 开始按钮点击事件
  onStartNow: function() {
    const { companyName, jobTitle, requirements, userName } = this.data

    // 验证输入
    if (!companyName.trim()) {
      tt.showToast({
        title: '请输入公司名称',
        icon: 'none'
      })
      return
    }

    if (!jobTitle.trim()) {
      tt.showToast({
        title: '请输入职位名称',
        icon: 'none'
      })
      return
    }

    if (!requirements.trim()) {
      tt.showToast({
        title: '请输入招聘要求',
        icon: 'none'
      })
      return
    }

    if (!userName.trim()) {
      tt.showToast({
        title: '请输入您的名字',
        icon: 'none'
      })
      return
    }

    // 保存用户输入的信息到全局
    const app = getApp()
    app.globalData.userInfo = {
      companyName,
      jobTitle,
      requirements,
      userName
    }

    // 跳转到公司信息锁定页面
    tt.navigateTo({
      url: '/pages/infolock/infolock'
    })
  }
})
