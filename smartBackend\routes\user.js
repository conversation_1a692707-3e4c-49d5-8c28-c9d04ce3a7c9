// backend/routes/user.js
import express from 'express'

import axios from 'axios'
import { query } from '../utils/database.js'; // <-- 这里只导入了 query，因为 user.js 示例只用了 query

const router = express.Router();
// 抖音配置
const DOUYIN_APP_ID = "tt04c2727f9bcae58501";
const DOUYIN_APP_SECRET = "d681be6d5670c7ffcade6c7120d56e302b82dae3";

// 获取用户OpenID并维护用户
router.post('/login', async (req, res) => {
    const { code } = req.body;
    const url = 'https://developer.toutiao.com/api/apps/jscode2session';
    try {
        // 1. 调用抖音接口获取 openId 和 session_key

        const { data }  = await axios.get(url, {
            params: {
                appid:  DOUYIN_APP_ID,
                secret: DOUYIN_APP_SECRET,
                code,
                }
            });
        console.log('抖音接口返回:', data); // 添加此行以查看抖音接口返回的数据
        const { openid, session_key } = data;

        if (!openid) {
            return res.status(400).json({ code: -1, msg: '获取OpenID失败' });
        }

        // 2. 查找或创建用户
        let user = await query('SELECT id FROM users WHERE douyin_openid = $1', [openid]);
        let userId;
        if (user.length > 0) {
            userId = user[0].id;
        } else {
            const insertRes = await query('INSERT INTO users (douyin_openid) VALUES ($1) RETURNING id', [openid]);
            console.log('用户插入结果:', insertRes);
            if (!insertRes || insertRes.length === 0) {
                throw new Error('用户创建失败');
            }
            userId = insertRes[0].id;
        }

        res.json({ code: 0, msg: '登录成功', data: { userId, openId: openid } });
    } catch (error) {
        console.error('用户登录失败:', error);
        res.status(500).json({ code: -1, msg: '服务器错误' });
    }
});

export default router;