# 数据库错误修复指南

## 问题描述
后端出现错误：
```
创建订单失败: TypeError: Cannot read properties of undefined (reading '0')
```

## 问题原因
1. **数据库查询结果处理错误**：代码试图访问 `orderInsertRes.rows[0].id`，但 `query` 函数已经返回了 `rows` 数组
2. **可能缺少数据库表结构**：orders表可能不存在或结构不正确

## 解决步骤

### 1. 初始化数据库表结构
```bash
cd smartBackend
npm run init-db
```

### 2. 测试数据库连接
```bash
npm run test-db
```

### 3. 验证修复结果
启动服务器并测试支付功能：
```bash
npm run dev
```

## 修复内容

### 1. 修正数据库查询结果处理
**修复前：**
```javascript
const orderInsertRes = await query('INSERT INTO orders (...) RETURNING id', [...]);
const orderId = orderInsertRes.rows[0].id; // ❌ 错误：多了一层 .rows
```

**修复后：**
```javascript
const orderInsertRes = await query('INSERT INTO orders (...) RETURNING id', [...]);
const orderId = orderInsertRes[0].id; // ✅ 正确：直接访问数组
```

### 2. 添加错误处理和日志
```javascript
console.log('数据库插入结果:', orderInsertRes);

if (!orderInsertRes || orderInsertRes.length === 0) {
    throw new Error('订单插入失败，未返回订单ID');
}
```

### 3. 创建完整的数据库表结构
- users 表：用户信息
- orders 表：订单信息
- company_info 表：公司信息
- interview_records 表：面试记录
- interview_questions 表：面试问题
- interview_answers 表：面试答案

## 数据库工具函数说明

`utils/database.js` 中的 `query` 函数：
```javascript
async function query(text, params) {
  const res = await pool.query(text, params);
  return res.rows; // 直接返回 rows 数组
}
```

因此在使用时：
- ✅ 正确：`const result = await query('SELECT * FROM users', []); const user = result[0];`
- ❌ 错误：`const result = await query('SELECT * FROM users', []); const user = result.rows[0];`

## 测试验证

### 1. 数据库连接测试
```bash
npm run test-db
```
应该看到：
```
✅ 数据库连接成功
✅ users - 存在
✅ orders - 存在
✅ 用户插入测试成功
```

### 2. 支付签名测试
```bash
npm run test-sign
```
应该看到：
```
✅ 签名生成成功
```

### 3. 完整流程测试
1. 启动后端服务：`npm run dev`
2. 在前端小程序中测试支付流程
3. 检查后端日志，应该看到：
   ```
   数据库插入结果: [{ id: 123 }]
   抖音支付API响应: { err_no: 0, ... }
   ```

## 常见问题

### Q: 仍然出现 "Cannot read properties of undefined"
A: 检查数据库表是否正确创建，运行 `npm run test-db` 验证

### Q: 数据库连接失败
A: 检查 `utils/database.js` 中的连接字符串是否正确

### Q: 订单插入失败
A: 检查 orders 表的字段是否与插入语句匹配

## 相关文件
- `smartBackend/routes/pay.js` - 支付路由（已修复）
- `smartBackend/routes/user.js` - 用户路由（已修复）
- `smartBackend/utils/database.js` - 数据库工具函数
- `smartBackend/init-database.sql` - 数据库表结构
- `smartBackend/init-db.js` - 数据库初始化脚本
- `smartBackend/test-db.js` - 数据库测试脚本
