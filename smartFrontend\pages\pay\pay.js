const app = getApp()

Page({
  data: {
    //selectedMethod: 'wechat' // 默认选择微信支付
    selectedMethod: 'douyin', // 默认选择抖音支付
    amount: 19.90, // 支付金额
    companyName: '', // 从上一个页面传递过来
    recruitmentRequirements: '',
    userName: '',
    userId: null, // 从后端获取的用户ID
    openId: null, // 从后端获取的OpenID
  },

  onLoad: function (options) {
    // 接收上一个页面传递过来的公司名称、招聘要求和姓名
    this.setData({
      companyName: options.companyName || '',
      recruitmentRequirements: options.recruitmentRequirements || '',
      userName: options.userName || ''
    });

    // 登录并获取OpenID，为后续支付和用户关联做准备
    this.userLogin();
  },

  userLogin: function() {
    tt.login({
      success: (res) => {
        if (res.code) {
          // 发送code到后端，换取openId和userId
          tt.request({
            url: 'https://01e6-240e-343-5849-4f00-4981-edb6-3ce0-db1a.ngrok-free.app/api/user/login', // 后端登录接口
            method: 'POST',
            data: { code: res.code },
            success: (loginRes) => {
              if (loginRes.data.code === 0) {
                this.setData({
                  userId: loginRes.data.data.userId,
                  openId: loginRes.data.data.openId,
                });
                // 可在此处存储到 globalData 或本地缓存，方便其他页面使用
                app.globalData.userId = loginRes.data.data.userId;
                app.globalData.openId = loginRes.data.data.openId;
              } else {
                tt.showToast({ title: '登录失败: ' + loginRes.data.msg, icon: 'none' });
              }
            },
            fail: (err) => {
              console.error('Login request failed:', err);
              tt.showToast({ title: '网络错误，登录失败', icon: 'none' });
            }
          });
        } else {
          console.log('Login failed: ' + res.errMsg);
          tt.showToast({ title: '抖音登录失败', icon: 'none' });
        }
      },
      fail: (err) => {
        console.error('tt.login failed:', err);
        tt.showToast({ title: '获取登录凭证失败', icon: 'none' });
      }
    });
  },

  // 选择支付方式 (如果只支持抖音支付，可以移除此逻辑)
  selectMethod: function(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedMethod: method
    });
  },

  // 支付按钮点击事件
  onPay: function() {
    // 检查是否已获取到用户ID和OpenID
    if (!this.data.userId || !this.data.openId) {
      tt.showToast({ title: '正在获取用户信息，请稍候', icon: 'none' });
      this.userLogin(); // 尝试重新登录
      return;
    }

    tt.showLoading({
      title: '正在创建订单...',
      mask: true
    });

    // 调用后端接口创建订单
    tt.request({
      url: 'https://01e6-240e-343-5849-4f00-4981-edb6-3ce0-db1a.ngrok-free.app/api/pay/createOrder', // 您的后端订单创建接口
      method: 'POST',
      data: {
        userId: this.data.userId,
        openId: this.data.openId,
        amount: this.data.amount,
        productDescription: 'SmartHire智能面试助手服务',
        companyName: this.data.companyName,
        recruitmentRequirements: this.data.recruitmentRequirements,
        userName: this.data.userName
      },
      success: (res) => {
        tt.hideLoading();
        console.log('创建订单响应:', res.data);
        if (res.data.code === 0) {
          const { order_token } = res.data.data;
          // 调用抖音支付API
          this.requestDouyinPayment(order_token);
        } else {
          console.error('创建订单失败:', res.data);
          tt.showToast({
            title: res.data.msg || '创建订单失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        tt.hideLoading();
        console.error('创建订单网络请求失败:', err);
        tt.showToast({
          title: '网络错误，创建订单失败',
          icon: 'none'
        });
      }
    });
  },

  // 调起抖音支付
  requestDouyinPayment: function(orderToken) {
    console.log('准备调起抖音支付，orderToken:', orderToken);
    tt.requestPayment({
      orderInfo: orderToken, // 直接使用后端返回的order_token
      success: (res) => {
        console.log('抖音支付成功:', res);
        tt.showToast({
          title: '支付成功！',
          icon: 'success',
          duration: 2000
        });
        // 支付成功后，立即请求公司信息并跳转
        this.fetchCompanyInfoAndNavigate();
      },
      fail: (res) => {
        console.error('抖音支付失败:', res);
        let msg = '支付失败';
        if (res.err_no === 1000) {
          msg = '支付已取消';
        } else if (res.err_no === 2000) {
          msg = '支付系统异常，请稍后重试';
        } else if (res.err_no === 3000) {
          msg = '支付参数错误';
        }
        tt.showToast({
          title: msg,
          icon: 'none'
        });
      }
    });
  },

  // 支付成功后，获取公司信息并跳转
  fetchCompanyInfoAndNavigate: function() {
    tt.showLoading({
      title: '正在获取公司信息...',
      mask: true
    });

    tt.request({
      url: 'https://01e6-240e-343-5849-4f00-4981-edb6-3ce0-db1a.ngrok-free.app/api/company/getInfo', // 您的后端获取公司信息接口
      method: 'GET',
      data: {
        companyName: this.data.companyName
      },
      success: (res) => {
        tt.hideLoading();
        if (res.data.code === 0 && res.data.data) {
          // 获取到公司信息，传递到下一个页面
          tt.redirectTo({
            url: `/pages/companyinfo/companyinfo?companyName=${encodeURIComponent(this.data.companyName)}&companyInfo=${encodeURIComponent(JSON.stringify(res.data.data))}&recruitmentRequirements=${encodeURIComponent(this.data.recruitmentRequirements)}&userName=${encodeURIComponent(this.data.userName)}`,
          });
        } else {
          tt.showToast({
            title: res.data.msg || '获取公司信息失败',
            icon: 'none'
          });
          // 即使获取失败，也跳转，但公司信息部分显示占位符或错误
          tt.redirectTo({
            url: `/pages/companyinfo/companyinfo?companyName=${encodeURIComponent(this.data.companyName)}&recruitmentRequirements=${encodeURIComponent(this.data.recruitmentRequirements)}&userName=${encodeURIComponent(this.data.userName)}&error=true`,
          });
        }
      },
      fail: (err) => {
        tt.hideLoading();
        console.error('获取公司信息网络请求失败:', err);
        tt.showToast({
          title: '获取公司信息网络错误',
          icon: 'none'
        });
        tt.redirectTo({
          url: `/pages/companyinfo/companyinfo?companyName=${encodeURIComponent(this.data.companyName)}&recruitmentRequirements=${encodeURIComponent(this.data.recruitmentRequirements)}&userName=${encodeURIComponent(this.data.userName)}&error=true`,
        });
      }
    });
  }




})
