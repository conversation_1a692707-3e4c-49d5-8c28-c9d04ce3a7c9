<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<meta charset="utf-8"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body>
<div class="relative flex size-full min-h-screen flex-col bg-white justify-between group/design-root overflow-x-hidden" style='font-family: Manrope, "Noto Sans", sans-serif;'>
<div class="flex flex-col items-center px-4 pt-8">
<h1 class="text-[#111418] text-3xl font-bold leading-tight tracking-tight text-center pb-8">SmartHire</h1>
<div class="w-full max-w-md space-y-6">
<div class="flex flex-col">
<label class="text-sm font-medium text-gray-700 mb-1.5" for="company-name">公司名称 (Company Name)</label>
<div class="flex w-full items-stretch rounded-xl border border-[#dbe0e6] bg-white shadow-sm focus-within:ring-2 focus-within:ring-[#0c7ff2] focus-within:border-[#0c7ff2]">
<div class="pl-4 pr-2 flex items-center pointer-events-none text-[#60758a]">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M240,208H224V96a16,16,0,0,0-16-16H144V32a16,16,0,0,0-24.88-13.32L39.12,72A16,16,0,0,0,32,85.34V208H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM208,96V208H144V96ZM48,85.34,128,32V208H48ZM112,112v16a8,8,0,0,1-16,0V112a8,8,0,1,1,16,0Zm-32,0v16a8,8,0,0,1-16,0V112a8,8,0,1,1,16,0Zm0,56v16a8,8,0,0,1-16,0V168a8,8,0,0,1,16,0Zm32,0v16a8,8,0,0,1-16,0V168a8,8,0,0,1,16,0Z"></path>
</svg>
</div>
<input class="form-input flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-none focus:ring-0 border-0 bg-transparent h-14 placeholder:text-gray-400 p-3 text-base leading-normal" id="company-name" placeholder="请输入公司名称" value=""/>
</div>
</div>
<div class="flex flex-col">
<label class="text-sm font-medium text-gray-700 mb-1.5" for="job-title">请输入职位名称 (Job Title)</label>
<div class="flex w-full items-stretch rounded-xl border border-[#dbe0e6] bg-white shadow-sm focus-within:ring-2 focus-within:ring-[#0c7ff2] focus-within:border-[#0c7ff2]">
<div class="pl-4 pr-2 flex items-center pointer-events-none text-[#60758a]">
<svg class="lucide lucide-briefcase" fill="none" height="20px" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="20px" xmlns="http://www.w3.org/2000/svg">
<rect height="14" rx="2" ry="2" width="20" x="2" y="7"></rect>
<path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
</svg>
</div>
<input class="form-input flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-none focus:ring-0 border-0 bg-transparent h-14 placeholder:text-gray-400 p-3 text-base leading-normal" id="job-title" placeholder="请输入职位名称" value=""/>
</div>
</div>
<div class="flex flex-col">
<label class="text-sm font-medium text-gray-700 mb-1.5" for="recruitment-requirements">招聘要求 (Recruitment Requirements)</label>
<div class="flex w-full items-stretch rounded-xl border border-[#dbe0e6] bg-white shadow-sm focus-within:ring-2 focus-within:ring-[#0c7ff2] focus-within:border-[#0c7ff2]">
<div class="pl-4 pr-2 flex items-center pointer-events-none text-[#60758a]">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M88,96a8,8,0,0,1,8-8h64a8,8,0,0,1,0,16H96A8,8,0,0,1,88,96Zm8,40h64a8,8,0,0,0,0-16H96a8,8,0,0,0,0,16Zm32,16H96a8,8,0,0,0,0,16h32a8,8,0,0,0,0-16ZM224,48V156.69A15.86,15.86,0,0,1,219.31,168L168,219.31A15.86,15.86,0,0,1,156.69,224H48a16,16,0,0,1-16-16V48A16,16,0,0,1,48,32H208A16,16,0,0,1,224,48ZM48,208H152V160a8,8,0,0,1,8-8h48V48H48Zm120-40v28.7L196.69,168Z"></path>
</svg>
</div>
<input class="form-input flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-none focus:ring-0 border-0 bg-transparent h-14 placeholder:text-gray-400 p-3 text-base leading-normal" id="recruitment-requirements" placeholder="请输入招聘要求" value=""/>
</div>
</div>
<div class="flex flex-col">
<label class="text-sm font-medium text-gray-700 mb-1.5" for="your-name">我的名字 (Your Name)</label>
<div class="flex w-full items-stretch rounded-xl border border-[#dbe0e6] bg-white shadow-sm focus-within:ring-2 focus-within:ring-[#0c7ff2] focus-within:border-[#0c7ff2]">
<div class="pl-4 pr-2 flex items-center pointer-events-none text-[#60758a]">
<svg fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px" xmlns="http://www.w3.org/2000/svg">
<path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
</svg>
</div>
<input class="form-input flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-none focus:ring-0 border-0 bg-transparent h-14 placeholder:text-gray-400 p-3 text-base leading-normal" id="your-name" placeholder="请输入您的名字" value=""/>
</div>
</div>
</div>
</div>
<div class="px-4 py-6 w-full max-w-md mx-auto">
<button class="flex w-full min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-14 px-5 bg-[#0c7ff2] text-white text-lg font-semibold leading-normal tracking-wide shadow-md hover:bg-blue-600 focus:ring-4 focus:ring-blue-300 focus:outline-none transition-colors duration-150">
<span class="truncate">立即开始 (Start Now)</span>
</button>
<div class="h-5"></div>
</div>
</div>

</body></html>