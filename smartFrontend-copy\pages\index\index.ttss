.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
  padding: 0 32rpx;
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64rpx 0;
}

.title {
  font-size: 60rpx;
  font-weight: bold;
  color: #111418;
  text-align: center;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 48rpx;
  max-width: 600rpx;
  margin: 0 auto;
  width: 100%;
}

.input-group {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border: 2rpx solid #dbe0e6;
  border-radius: 24rpx;
  background-color: #ffffff;
  padding: 0 32rpx;
  height: 112rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.input-wrapper:focus-within {
  border-color: #0c7ff2;
  box-shadow: 0 0 0 6rpx rgba(12, 127, 242, 0.1);
}

.icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  color: #60758a;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #111418;
  background: transparent;
  border: none;
  outline: none;
}

.input::placeholder {
  color: #9ca3af;
}

.button-container {
  padding: 48rpx 0 96rpx 0;
  max-width: 600rpx;
  margin: 0 auto;
  width: 100%;
}

.start-button {
  width: 100%;
  height: 112rpx;
  background-color: #0c7ff2;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;
  border-radius: 24rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(12, 127, 242, 0.3);
  transition: all 0.2s ease;
}

.start-button:active {
  background-color: #0968c3;
  transform: translateY(2rpx);
}