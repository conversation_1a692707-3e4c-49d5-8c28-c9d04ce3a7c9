<view class="container">
  <view class="header">
    <button class="back-button" bindtap="goBack">
      <text class="back-icon">‹</text>
    </button>
    <text class="header-title">关于{{companyName}}</text>
    <button class="help-button" bindtap="showHelp">
      <text class="help-icon">?</text>
    </button>
  </view>
  
  <view class="content">
    <view class="tip-section">
      <text class="tip-text">💡 准备充足，面试更有底气！</text>
    </view>
    
    <view class="info-section">
      <text class="section-title">公司信息</text>
      <view class="info-content">
        <text class="info-text">{{companyInfo}}</text>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <button class="start-interview-button" bindtap="startInterview">
      开始模拟面试
    </button>
  </view>
</view>
